namespace OJS.Services.Infrastructure.Models;

using OJS.Common.Constants;

public class ValidationResult
{
    protected ValidationResult()
    {
    }

    public bool IsValid { get; set; }

    public virtual string Message { get; set; } = string.Empty;

    public string? PropertyName { get; set; }

    public string? ErrorCode { get; set; }

    public static ValidationResult Valid()
        => new()
        {
            IsValid = true,
        };

    public static ValidationResult Invalid(string message)
        => new()
        {
            Message = message,
            ErrorCode = ServiceConstants.ErrorCodes.BusinessRuleViolation,
        };

    public static ValidationResult Invalid(string message, string errorCode)
        => new()
        {
            Message = message,
            ErrorCode = errorCode,
        };
}