namespace OJS.Services.Ui.Business.Implementations;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using OJS.Common;
using OJS.Common.Enumerations;
using OJS.Data.Models.Participants;
using OJS.Data.Models.Submissions;
using OJS.Data.Models.Tests;
using OJS.PubSub.Worker.Models.Submissions;
using OJS.Services.Common;
using OJS.Services.Common.Data;
using OJS.Services.Common.Models.Submissions;
using OJS.Services.Infrastructure.Exceptions;
using OJS.Services.Infrastructure.Extensions;
using OJS.Services.Ui.Business.Validations.Implementations.Contests;
using OJS.Services.Ui.Business.Validations.Implementations.Submissions;
using OJS.Services.Ui.Data;
using OJS.Services.Ui.Models.Participants;
using OJS.Services.Ui.Models.Submissions;
using OJS.Workers.Common.Models;
using OJS.Services.Infrastructure;
using OJS.Services.Infrastructure.Cache;
using OJS.Services.Infrastructure.Constants;
using OJS.Services.Infrastructure.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using OJS.Data.Models.Problems;
using OJS.Services.Common.Data.Pagination;
using OJS.Services.Common.Models;
using OJS.Services.Common.Models.Pagination;
using OJS.Services.Ui.Business.Cache;
using OJS.Workers.Common.Extensions;
using static OJS.Common.GlobalConstants.Submissions;
using static OJS.Services.Common.Constants.PaginationConstants.Submissions;
using static OJS.Services.Infrastructure.Models.ModelHelpers;
using static OJS.Services.Ui.Business.Constants.Comments;

public class SubmissionsBusinessService : ISubmissionsBusinessService
{
    private readonly ILogger<SubmissionsBusinessService> logger;
    private readonly ISubmissionsDataService submissionsData;
    private readonly ISubmissionsCommonDataService submissionsCommonData;
    private readonly ISubmissionsForProcessingCommonDataService submissionsForProcessingData;
    private readonly IUsersBusinessService usersBusiness;
    private readonly IParticipantScoresBusinessService participantScoresBusinessService;
    private readonly ISubmissionsCommonBusinessService submissionsCommonBusinessService;
    private readonly IParticipantsDataService participantsDataService;
    private readonly IProblemsDataService problemsDataService;
    private readonly IUserProviderService userProviderService;
    private readonly ILecturersInContestsCacheService lecturersInContestsCache;
    private readonly ISubmissionDetailsValidationService submissionDetailsValidationService;
    private readonly ISubmitSubmissionValidationService submitSubmissionValidationService;
    private readonly ISubmissionFileDownloadValidationService submissionFileDownloadValidationService;
    private readonly IRetestSubmissionValidationService retestSubmissionValidationService;
    private readonly IPublisherService publisher;
    private readonly ISubmissionsHelper submissionsHelper;
    private readonly IDatesService dates;
    private readonly ITransactionsProvider transactionsProvider;
    private readonly ICacheService cache;
    private readonly ITestRunsDataService testRunsDataService;
    private readonly IProblemsCacheService problemsCache;
    private readonly IContestCategoriesCacheService contestCategoriesCache;
    private readonly IFilteringService filteringService;
    private readonly ISortingService sortingService;
    private readonly IFileIoService fileIo;
    private readonly IFileSystemService fileSystem;

    public SubmissionsBusinessService(
        ILogger<SubmissionsBusinessService> logger,
        ISubmissionsDataService submissionsData,
        ISubmissionsCommonDataService submissionsCommonData,
        IUsersBusinessService usersBusiness,
        IProblemsDataService problemsDataService,
        IParticipantsDataService participantsDataService,
        ISubmissionsCommonBusinessService submissionsCommonBusinessService,
        IUserProviderService userProviderService,
        IParticipantScoresBusinessService participantScoresBusinessService,
        ILecturersInContestsCacheService lecturersInContestsCache,
        ISubmissionDetailsValidationService submissionDetailsValidationService,
        ISubmitSubmissionValidationService submitSubmissionValidationService,
        ISubmissionFileDownloadValidationService submissionFileDownloadValidationService,
        IRetestSubmissionValidationService retestSubmissionValidationService,
        ISubmissionsForProcessingCommonDataService submissionsForProcessingData,
        IPublisherService publisher,
        ISubmissionsHelper submissionsHelper,
        IDatesService dates,
        ITransactionsProvider transactionsProvider,
        ICacheService cache,
        ITestRunsDataService testRunsDataService,
        IProblemsCacheService problemsCache,
        IFilteringService filteringService,
        ISortingService sortingService,
        IContestCategoriesCacheService contestCategoriesCache,
        IFileIoService fileIo,
        IFileSystemService fileSystem)
    {
        this.logger = logger;
        this.submissionsData = submissionsData;
        this.submissionsCommonData = submissionsCommonData;
        this.usersBusiness = usersBusiness;
        this.problemsDataService = problemsDataService;
        this.lecturersInContestsCache = lecturersInContestsCache;
        this.submissionsCommonBusinessService = submissionsCommonBusinessService;
        this.participantsDataService = participantsDataService;
        this.userProviderService = userProviderService;
        this.participantScoresBusinessService = participantScoresBusinessService;
        this.submissionDetailsValidationService = submissionDetailsValidationService;
        this.submitSubmissionValidationService = submitSubmissionValidationService;
        this.submissionFileDownloadValidationService = submissionFileDownloadValidationService;
        this.retestSubmissionValidationService = retestSubmissionValidationService;
        this.publisher = publisher;
        this.submissionsForProcessingData = submissionsForProcessingData;
        this.submissionsHelper = submissionsHelper;
        this.dates = dates;
        this.transactionsProvider = transactionsProvider;
        this.cache = cache;
        this.testRunsDataService = testRunsDataService;
        this.problemsCache = problemsCache;
        this.contestCategoriesCache = contestCategoriesCache;
        this.filteringService = filteringService;
        this.sortingService = sortingService;
        this.fileIo = fileIo;
        this.fileSystem = fileSystem;
    }

    public async Task Retest(int submissionId, bool verbosely = false)
    {
        var submission = await this.submissionsData.GetSubmissionById<SubmissionForRetestServiceModel>(submissionId)
            ?? throw new BusinessServiceException(ValidationMessages.Submission.NotFound);

        var user = this.userProviderService.GetCurrentUser();

        var userIsAdminOrLecturerInContest = await this.lecturersInContestsCache
            .IsUserAdminOrLecturerInContest(submission.ContestId, submission.ContestCategoryId, user);

        var validationResult = await this.retestSubmissionValidationService.GetValidationResult((
                submission,
                user,
                userIsAdminOrLecturerInContest));

        if (!validationResult.IsValid)
        {
            throw new BusinessServiceException(validationResult.Message);
        }

        verbosely = verbosely && userIsAdminOrLecturerInContest;
        await this.publisher.Publish(new RetestSubmissionPubSubModel { Id = submissionId, Verbosely = verbosely });
    }

    public async Task<SubmissionDetailsServiceModel> GetDetailsById(int submissionId)
    {
        var submission = await this.submissionsData.GetSubmissionById<SubmissionDetailsServiceModel>(submissionId)
            ?? throw new BusinessServiceException(ValidationMessages.Submission.NotFound);

        var currentUser = this.userProviderService.GetCurrentUser();

        var userAdminOrLecturerInContest = await this.lecturersInContestsCache
            .IsUserAdminOrLecturerInContest(submission.ContestId, submission.ContestCategoryId, currentUser);

        var validationResult = this.submissionDetailsValidationService
            .GetValidationResult((submission, currentUser, userAdminOrLecturerInContest));

        if (!validationResult.IsValid)
        {
            throw new BusinessServiceException(validationResult.Message);
        }

        submission.UserIsInRoleForContest = userAdminOrLecturerInContest;

        var category = await this.contestCategoriesCache.GetById(submission.ContestCategoryId);
        submission.AllowMentor = category?.AllowMentor ?? false;

        if (!submission.IsCompiledSuccessfully)
        {
            // If the submission is not compiled successfully, we do not need to load the test runs.
            return submission;
        }

        var testRuns = await this.testRunsDataService
            .GetAllBySubmission(submissionId)
            .AsNoTracking()
            .MapCollection<TestRunDetailsServiceModel>()
            .ToListAsync();

        foreach (var testRun in testRuns)
        {
            var test = testRun.Test;
            submission.Tests.Add(test);

            testRun.ShowInput = submission.UserIsInRoleForContest || (test is { HideInput: false }
                               && (test.IsTrialTest
                                   || test.IsOpenTest
                                   || submission.Problem.ShowDetailedFeedback));

            var showExecutionComment = submission.UserIsInRoleForContest || (!string.IsNullOrEmpty(testRun.ExecutionComment)
                                   && (test.IsOpenTest
                                       || test.IsTrialTest
                                       || submission.Problem.ShowDetailedFeedback));

            if (!showExecutionComment)
            {
                testRun.ExecutionComment = string.Empty;
            }

            if (testRun.ShowInput)
            {
                testRun.Input = test.InputData?.Decompress();
            }
            else
            {
                testRun.Input = string.Empty;
                testRun.ExpectedOutputFragment = string.Empty;
                testRun.UserOutputFragment = string.Empty;
            }
        }

        submission.TestRuns = [.. testRuns.OrderBy(tr => !tr.IsTrialTest).ThenBy(tr => tr.OrderBy)];
        submission.IsEligibleForRetest = await this.submissionsHelper.IsEligibleForRetest(
            submissionId, submission.IsProcessed, submission.IsCompiledSuccessfully, submission.TestRuns.Count);

        submission.AllowMentor = submission.AllowMentor && submission.TestRuns.Any(tr => tr.ResultType != TestRunResultType.CorrectAnswer);

        return submission;
    }

    public async Task<SubmissionFileDownloadServiceModel> GetSubmissionFile(int submissionId)
    {
        var submissionDetailsServiceModel = await this.submissionsData
            .GetSubmissionById<SubmissionFileDetailsServiceModel>(submissionId);

        var currentUser = this.userProviderService.GetCurrentUser();

        var validationResult =
            this.submissionFileDownloadValidationService.GetValidationResult((submissionDetailsServiceModel!,
                currentUser));

        if (!validationResult.IsValid)
        {
            throw new BusinessServiceException(validationResult.Message);
        }

        return new SubmissionFileDownloadServiceModel
        {
            Content = submissionDetailsServiceModel!.ByteContent,
            MimeType = GlobalConstants.MimeTypes.ApplicationOctetStream,
            FileName = string.Format(
                SubmissionDownloadFileName,
                submissionDetailsServiceModel.Id,
                submissionDetailsServiceModel.FileExtension),
        };
    }

    public Task<Dictionary<SubmissionProcessingState, int>> GetAllUnprocessedCount()
        => this.submissionsForProcessingData
            .GetAllUnprocessed()
            .GroupBy(sfp => sfp.State)
            .ToDictionaryAsync(sfp => sfp.Key, sfp => sfp.Count());

    public string GetLogFilePath(int submissionId)
        => this.fileSystem.BuildPath(
            this.fileSystem.GetTempDirectory("submission-logs"),
            $"submission-{submissionId}.log");

    public async Task<PagedResult<TServiceModel>> GetByUsername<TServiceModel>(
        string? username,
        PaginationRequestModel requestModel)
    {
        if (!await this.usersBusiness.IsUserInRolesOrProfileOwner(
                username,
                [GlobalConstants.Roles.Administrator, GlobalConstants.Roles.Lecturer]))
        {
            throw new UnauthorizedAccessException("You are not authorized for this action");
        }

        var userId = await this.usersBusiness.GetUserIdByUsername(username);

        var userParticipantsIds = await this.participantsDataService
            .GetAllByUser(userId)
            .Select(p => p.Id)
            .ToListAsync();

        var query = this.submissionsData.GetLatestSubmissionsByUserParticipations(userParticipantsIds);

        return await
            this.ApplyFiltersAndSorters<TServiceModel>(requestModel, query)
                .ToPagedResultAsync(requestModel.ItemsPerPage, requestModel.Page);
    }

    public async Task<ServiceResult<PagedResult<SubmissionForSubmitSummaryServiceModel>>> GetUserSubmissionsByProblem(
        int problemId,
        bool isOfficial,
        PaginationRequestModel requestModel)
    {
        var problem =
            await this.problemsDataService.OneByIdTo<ProblemForSubmissionDetailsServiceModel>(problemId);

        if (problem == null)
        {
            return ServiceResult.NotFound<PagedResult<SubmissionForSubmitSummaryServiceModel>>(nameof(Problem), context: new { problemId });
        }

        var user = this.userProviderService.GetCurrentUser();

        var participant = await this.participantsDataService
            .GetAllByContestByUserAndIsOfficial(problem?.ProblemGroupContestId ?? 0, user.Id, isOfficial)
            .AsNoTracking()
            .MapCollection<ParticipantServiceModel>()
            .FirstOrDefaultAsync();

        if (participant == null)
        {
            return ServiceResult.NotFound<PagedResult<SubmissionForSubmitSummaryServiceModel>>(nameof(Participant), context: new { problemId, isOfficial });
        }

        var query = this.submissionsData
            .GetAllByProblemAndParticipant(problemId, participant.Id);

        var submissions = await
            this.ApplyFiltersAndSorters<SubmissionForSubmitSummaryServiceModel>(requestModel, query)
                .ToPagedResultAsync(requestModel.ItemsPerPage, requestModel.Page);

        foreach (var submission in submissions.Items)
        {
            var (maxMemoryUsed, maxTimeUsed) = GetMaxMemoryAndTimeUsed(submission.TestRunsCache);

            submission.IsOfficial = isOfficial;
            submission.MaxMemoryUsed = maxMemoryUsed;
            submission.MaxTimeUsed = maxTimeUsed;
            submission.Result.MaxPoints = problem!.MaximumPoints;
        }

        return ServiceResult.Success(submissions);
    }

    public async Task<ServiceResult<VoidResult>> Submit(SubmitSubmissionServiceModel model)
    {
        var problem = await this.problemsCache.GetForSubmitById(model.ProblemId);
        if (problem == null)
        {
            return ServiceResult.NotFound<VoidResult>(nameof(Problem));
        }

        var currentUser = this.userProviderService.GetCurrentUser();

        // Using .Select instead of AutoMapper due to conditional mapping, depending on model state.
        // ProblemsForParticipants are not needed in most cases, so they are not included in the query.
        var participant = await this.participantsDataService
            .GetAllByContestByUserAndIsOfficial(model.ContestId, currentUser.Id, model.Official)
            .AsNoTracking()
            .Select(p => new ParticipantSubmitServiceModel
            {
                Id = p.Id,
                IsInvalidated = p.IsInvalidated,
                IsOfficial = p.IsOfficial,
                ContestId = p.ContestId,
                ContestType = p.Contest.Type,
                ContestCategoryId = p.Contest.CategoryId,
                LastSubmissionTime = p.LastSubmissionTime,
                ParticipationStartTime = p.ParticipationStartTime,
                ParticipationEndTime = p.ParticipationEndTime,
                ContestStartTime = p.Contest.StartTime,
                ContestEndTime = p.Contest.EndTime,
                ContestPracticeStartTime = p.Contest.PracticeStartTime,
                ContestPracticeEndTime = p.Contest.PracticeEndTime,
                ContestLimitBetweenSubmissions = p.Contest.LimitBetweenSubmissions,
                ContestAllowParallelSubmissionsInTasks = p.Contest.AllowParallelSubmissionsInTasks,
                Problems = model.Official && model.IsWithRandomTasks
                    ? p.ProblemsForParticipants
                        .Select(pfp => new ProblemForParticipantServiceModel
                        {
                            ProblemId = pfp.ProblemId,
                            ParticipantId = pfp.ParticipantId,
                        })
                    : null,
            })
            .FirstOrDefaultAsync();

        var submissionType = problem.SubmissionTypesInProblems
            .Select(p => p.SubmissionType)
            .MapCollection<SubmissionType>()
            .FirstOrDefault(st => st.Id == model.SubmissionTypeId);

        var submitSubmissionValidationServiceResult = await this.submitSubmissionValidationService.GetValidationResult(
            (problem, participant, model, submissionType, currentUser));

        if (!submitSubmissionValidationServiceResult.IsValid)
        {
            return submitSubmissionValidationServiceResult.ToServiceResult<VoidResult>();
        }

        var newSubmission = model.Map<Submission>();
        if (model.StringContent != null)
        {
            newSubmission.ContentAsString = model.StringContent;
        }
        else
        {
            newSubmission.Content = model.ByteContent!;
        }

        newSubmission.ParticipantId = participant!.Id;

        var updatedParticipant = new Participant
        {
            Id = participant.Id,
            LastSubmissionTime = this.dates.GetUtcNow(),
        };

        this.participantsDataService.Attach(updatedParticipant);
        this.participantsDataService
            .GetEntry(updatedParticipant)
            .Property(p => p.LastSubmissionTime).IsModified = true;

        if (submissionType!.ExecutionStrategyType
            is ExecutionStrategyType.NotFound
            or ExecutionStrategyType.DoNothing)
        {
            // Submission is just uploaded and should not be processed
            await this.AddNewDefaultProcessedSubmission(participant.Id, newSubmission);
            return ServiceResult.EmptySuccess;
        }

        if (problem.HasAdditionalFiles)
        {
            problem.AdditionalFiles = await this.problemsDataService
                .GetByIdQuery(model.ProblemId)
                .Select(p => p.AdditionalFiles)
                .FirstOrDefaultAsync();
        }

        SubmissionForProcessing? submissionForProcessing = null;
        await this.transactionsProvider.ExecuteInTransaction(async () =>
        {
            await this.submissionsData.Add(newSubmission);
            await this.submissionsData.SaveChanges();

            submissionForProcessing = await this.submissionsForProcessingData.Add(newSubmission.Id);
            await this.submissionsData.SaveChanges();
        });

        var submissionServiceModel =
            this.submissionsCommonBusinessService.BuildSubmissionForProcessing(newSubmission, problem.Map<Problem>(),
                submissionType, executeVerbosely: model.Verbosely && currentUser.IsAdminOrLecturer);

        await this.submissionsCommonBusinessService.PublishSubmissionForProcessing(submissionServiceModel,
            submissionForProcessing!);

        return ServiceResult.EmptySuccess;
    }

    public async Task ProcessExecutionResult(SubmissionExecutionResult submissionExecutionResult)
    {
        var submission = await this.submissionsData
            .GetByIdQuery(submissionExecutionResult.SubmissionId)
            .IgnoreQueryFilters()
            .Include(s => s.TestRuns)
            .FirstOrDefaultAsync()
            ?? throw new BusinessServiceException(
                $"Submission with Id: \"{submissionExecutionResult.SubmissionId}\" not found.");

        var submissionForProcessing = await this.submissionsForProcessingData
            .GetBySubmission(submission.Id)
            ?? throw new BusinessServiceException(
                $"Submission for processing for Submission with ID {submission.Id} not found in the database.");

        var participant = await this.participantsDataService
            .GetByIdQuery(submission.ParticipantId)
            .IgnoreQueryFilters()
            .Include(p => p.User)
            .FirstOrDefaultAsync()
            ?? throw new BusinessServiceException(
                $"Participant with Id: \"{submission.ParticipantId}\" not found.");

        var exception = submissionExecutionResult.Exception;
        var executionResult = submissionExecutionResult.ExecutionResult;

        await this.transactionsProvider.ExecuteInTransaction(async () =>
        {
            submission.Processed = true;
            submission.ProcessingComment = null;
            submission.StartedExecutionOn = submissionExecutionResult.StartedExecutionOn;
            submission.CompletedExecutionOn = submissionExecutionResult.CompletedExecutionOn;
            submission.WorkerName = submissionExecutionResult.WorkerName;

            if (executionResult != null)
            {
                submission.ProcessingComment = executionResult.ProcessingComment;
                ProcessTestsExecutionResult(submission, executionResult);
                await this.SaveParticipantScore(participant, submission);
                CacheTestRuns(submission);
            }
            else
            {
                submission.ExceptionType = exception?.ExceptionType ?? ExceptionType.Other;
                submission.IsCompiledSuccessfully = false;

                var errorMessage = exception?.Message ?? "Invalid execution result received. Please contact an administrator.";
                var isRegularUserError = exception?.ExceptionType == ExceptionType.Solution;
                submission.ProcessingComment = isRegularUserError ? "See the compiler comment for more details." : errorMessage;
                submission.CompilerComment = isRegularUserError ? errorMessage : ProcessingExceptionCompilerComment;
            }

            await this.submissionsForProcessingData.SetProcessingState(submissionForProcessing, SubmissionProcessingState.Processed);
        });

        if (submissionExecutionResult.VerboseLogFile is { Length: > 0 })
        {
            await this.fileIo.SaveFile(this.GetLogFilePath(submission.Id), submissionExecutionResult.VerboseLogFile);
        }

        this.logger.LogSubmissionProcessedSuccessfully(submission.Id, submissionForProcessing);
    }

    public Task<int> GetTotalCount()
        => this.cache.Get(
            CacheConstants.TotalSubmissionsCount,
            async () => await this.submissionsData.IgnoreQueryFilters().Count(),
            CacheConstants.FiveMinutesInSeconds);

    public async Task<PagedResult<TServiceModel>> GetSubmissions<TServiceModel>(
        SubmissionStatus status,
        PaginationRequestModel requestModel)
    {
        if (requestModel.ItemsPerPage <= 0)
        {
            throw new BusinessServiceException("Invalid submissions per page count");
        }

        IQueryable<Submission> query;

        if (status == SubmissionStatus.Enqueued)
        {
            query = this.submissionsCommonData.GetAllEnqueued();
        }
        else if (status == SubmissionStatus.Processing)
        {
            query = this.submissionsCommonData.GetAllProcessing();
        }
        else if (status == SubmissionStatus.Pending)
        {
            query = this.submissionsCommonData.GetAllPending();
        }
        else
        {
            if (!this.userProviderService.GetCurrentUser().IsAdminOrLecturer)
            {
                return await this.cache.Get(
                    CacheConstants.LatestPublicSubmissions,
                    async () =>
                    {
                        var submissions = await this.submissionsData
                            .GetLatestSubmissions<TServiceModel>(DefaultSubmissionsPerPage)
                            .ToListAsync();

                        var totalItemsCount = await this.GetTotalCount();

                        // Public submissions do not have pagination, but PagedResult is used for consistency.
                        return new PagedResult<TServiceModel>
                        {
                            Items = submissions,
                            TotalItemsCount = totalItemsCount,
                            PageNumber = 1,
                        };
                    },
                    CacheConstants.TwoMinutesInSeconds);
            }

            query = this.submissionsData.GetQuery(
                orderBy: s => s.Id,
                descending: true);
        }

        var result = await
            this.ApplyFiltersAndSorters<TServiceModel>(requestModel, query)
                .ToPagedResultAsync(requestModel.ItemsPerPage, requestModel.Page);

        return result;
    }

    private static void ProcessTestsExecutionResult(
        Submission submission,
        ExecutionResultServiceModel executionResult)
    {
        submission.IsCompiledSuccessfully = executionResult.IsCompiledSuccessfully;
        submission.CompilerComment = executionResult.CompilerComment;
        submission.Points = executionResult.TaskResult!.Points;

        if (!executionResult.IsCompiledSuccessfully)
        {
            submission.TestRuns.Clear();
            return;
        }

        submission.TestRuns = (executionResult.TaskResult?.TestResults ?? []).Select(testResult => new TestRun
        {
            ResultType = testResult.ResultType,
            CheckerComment = testResult.CheckerDetails?.Comment,
            ExecutionComment = testResult.ExecutionComment,
            ExpectedOutputFragment = testResult.CheckerDetails?.ExpectedOutputFragment,
            UserOutputFragment = testResult.CheckerDetails?.UserOutputFragment,
            IsTrialTest = testResult.IsTrialTest,
            TimeUsed = testResult.TimeUsed,
            MemoryUsed = testResult.MemoryUsed,
            SubmissionId = submission.Id,
            TestId = testResult.Id,
        }).ToList();
    }


    private static void HandleProcessingException(Submission submission, Exception ex, string methodName)
    {
        submission.ProcessingComment = string.Format(ProcessingException, methodName, ex.Message);
        submission.IsCompiledSuccessfully = false;
        submission.CompilerComment = ProcessingExceptionCompilerComment;
        submission.TestRuns = [];
    }

    private static void CacheTestRuns(Submission submission)
    {
        try
        {
            submission.CacheTestRuns();
        }
        catch (Exception ex)
        {
            HandleProcessingException(submission, ex, nameof(CacheTestRuns));
        }
    }

    private async Task AddNewDefaultProcessedSubmission(int participantId, Submission submission)
    {
        var participant = await this.participantsDataService
            .GetByIdQuery(participantId)
            .Include(p => p.User)
            .FirstOrDefaultAsync()
            ?? throw new BusinessServiceException($"Participant with Id: \"{participantId}\" not found.");

        submission.Processed = true;
        submission.IsCompiledSuccessfully = true;
        submission.Points = 0;

        await this.submissionsData.Add(submission);
        await this.submissionsData.SaveChanges();

        await this.participantScoresBusinessService.SaveForSubmission(participant, submission);
    }

    private async Task SaveParticipantScore(Participant participant, Submission submission)
    {
        try
        {
            await this.participantScoresBusinessService.SaveForSubmission(participant, submission);
        }
        catch (Exception ex)
        {
            HandleProcessingException(submission, ex, nameof(this.SaveParticipantScore));
        }
    }

    private IQueryable<TModel> ApplyFiltersAndSorters<TModel>(PaginationRequestModel paginationRequestModel, IQueryable<Submission> query)
    {
        if (string.IsNullOrWhiteSpace(paginationRequestModel.Filter) && string.IsNullOrWhiteSpace(paginationRequestModel.Sorting))
        {
            return query.MapCollection<TModel>();
        }

        var filterAsCollection = this.filteringService.MapFilterStringToCollection<TModel>(paginationRequestModel).ToList();

        var mappedQuery = this.filteringService.ApplyFiltering<Submission, TModel>(query.AsNoTracking(), filterAsCollection);

        return this.sortingService
            .ApplySorting(mappedQuery, paginationRequestModel.Sorting);
    }
}