namespace OJS.Workers.Common.Helpers
{
    using System;
    using System.Collections.Generic;
    using System.IO;
    using System.Linq;

    using Ionic.Zip;
    using OJS.Workers.Common.Exceptions;
    using System.IO.Compression;
    using System.Text;
    using Zip = System.IO.Compression.ZipFile;
    using ZipFile = Ionic.Zip.ZipFile;

    // TODO: Unit test
    public static class FileHelpers
    {
        public static string SaveStringToFile(string stringToWrite, string filePath)
        {
            File.WriteAllText(filePath, stringToWrite);
            return filePath;
        }

        public static string SaveStringToTempFile(string directory, string stringToWrite)
        {
            var tempFilePath = Path.GetTempFileName();
            File.Delete(tempFilePath);
            var fullTempFilePath = Path.Combine(directory, Path.GetFileName(tempFilePath));
            File.WriteAllText(fullTempFilePath, stringToWrite);
            return fullTempFilePath;
        }

        public static string SaveByteArrayToTempFile(string directory, byte[] dataToWrite)
        {
            var tempFilePath = Path.GetTempFileName();
            File.Delete(tempFilePath);
            var fullTempFilePath = Path.Combine(directory, Path.GetFileName(tempFilePath));
            File.WriteAllBytes(fullTempFilePath, dataToWrite);
            return fullTempFilePath;
        }

        public static void UnzipFile(string fileToUnzip, string outputDirectory)
            => Zip.ExtractToDirectory(fileToUnzip, outputDirectory);

        public static void UnzipFileAndOverwriteExistingFiles(string fileToUnzip, string outputDirectory)
            => Zip.ExtractToDirectory(fileToUnzip, outputDirectory, overwriteFiles: true);

        public static string FindFileMatchingPattern(string workingDirectory, string pattern)
        {
            var files = DiscoverAllFilesMatchingPattern(workingDirectory, pattern);

            var discoveredFile = files.First();

            return ProcessModulePath(discoveredFile);
        }

        public static string FindFileMatchingPattern<TOut>(
            string workingDirectory,
            string pattern,
            Func<string, TOut> orderBy)
        {
            var files = DiscoverAllFilesMatchingPattern(workingDirectory, pattern);

            var discoveredFile = files.OrderByDescending(orderBy).First();

            return ProcessModulePath(discoveredFile);
        }

        public static IEnumerable<string> FindAllFilesMatchingPattern(
            string workingDirectory,
            string pattern)
        {
            var files = DiscoverAllFilesMatchingPattern(workingDirectory, pattern);

            return files.Select(ProcessModulePath).ToList();
        }

        public static void AddFilesToZipArchive(string archivePath, string pathInArchive, params string[] filePaths)
        {
            using var zipFile = new ZipFile(archivePath);
            zipFile.UpdateFiles(filePaths, pathInArchive);
            zipFile.Save();
        }

        public static IEnumerable<string> GetFilePathsFromZip(string archivePath)
        {
            using var file = new ZipFile(archivePath);
            return file.EntryFileNames;
        }

        public static void RemoveFilesFromZip(string pathToArchive, string pattern)
        {
            using var file = new ZipFile(pathToArchive);
            file.RemoveSelectedEntries(pattern);
            file.Save();
        }

        public static void DeleteFiles(params string[] filePaths)
        {
            foreach (var filePath in filePaths)
            {
                DeleteFile(filePath);
            }
        }

        public static void DeleteFile(string filePath)
        {
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }
        }

        public static string ReadFile(string filePath)
            => File.ReadAllText(filePath);

        public static string ExtractFileFromZip(string pathToArchive, string fileName, string destinationDirectory)
        {
            using var zip = new ZipFile(pathToArchive);

            var entryToExtract = zip.Entries.FirstOrDefault(f => f.FileName.EndsWith(fileName));
            if (entryToExtract == null)
            {
                throw new SolutionException($"The file \"{fileName}\" was not found in the submission!");
            }

            entryToExtract.Extract(destinationDirectory);

            var extractedFilePath = $"{destinationDirectory}{Path.DirectorySeparatorChar}{entryToExtract.FileName.Replace("/", Path.DirectorySeparatorChar.ToString())}";

            return extractedFilePath;
        }

        public static bool FileExistsInZip(string pathToArchive, string fileName)
        {
            using var zip = new ZipFile(pathToArchive);
            var entryToExtract = zip.Entries.FirstOrDefault(f => f.FileName.EndsWith(fileName));
            if (entryToExtract == null)
            {
                return false;
            }

            return true;
        }

        public static string ProcessModulePath(string path) => path.Replace('\\', '/');

        public static string BuildPath(params string[] paths) => Path.Combine(paths);

        public static string BuildSubmissionLogFilePath(int submissionId)
            => Path.Combine(Path.GetTempPath(), $"submission-{submissionId}.log");

        public static void WriteAllText(string filePath, string text)
            => File.WriteAllText(filePath, text);

        public static void WriteAllBytes(string filePath, byte[] data)
            => File.WriteAllBytes(filePath, data);

        public static bool FileExists(string filePath) => File.Exists(filePath);

        public static async Task<byte[]> ReadFileUpToBytes(string filePath, int maxBytes)
        {
            // If the file is less than 10 MB, read the entire file
            var fileInfo = new FileInfo(filePath);
            if (fileInfo.Length <= maxBytes)
            {
                return await File.ReadAllBytesAsync(filePath);
            }

            // Otherwise, read only the first 10 MB
            var buffer = new byte[maxBytes];
            await using var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
            await stream.ReadExactlyAsync(buffer, 0, maxBytes);
            return buffer;
        }

        /// <summary>
        /// Rezips the given zip file, removing all extra metadata.
        /// This prevents some issues with zip files created on macOS or older versions of Windows.
        /// </summary>
        public static byte[] Rezip(byte[] zipBytes)
        {
            using var inMs = new MemoryStream(zipBytes);
            using var src = ZipFile.Read(inMs);
            src.AlternateEncoding = Encoding.UTF8;
            src.AlternateEncodingUsage = ZipOption.Always;

            using var outMs = new MemoryStream();
            using var dst = new ZipFile();
            dst.AlternateEncoding = Encoding.UTF8;
            dst.AlternateEncodingUsage = ZipOption.Always;
            dst.UseZip64WhenSaving = Zip64Option.AsNecessary;
            dst.EmitTimesInUnixFormatWhenSaving = true;
            dst.EmitTimesInWindowsFormatWhenSaving = false;
            dst.Encryption = EncryptionAlgorithm.None;
            dst.CompressionLevel = Ionic.Zlib.CompressionLevel.Default;

            foreach (var e in src.EntriesSorted)
            {
                if (e.IsDirectory)
                {
                    continue;
                }

                var name = e.FileName.Replace('\\', '/');
                var newEntry = dst.AddEntry(name, (n, outStream) =>
                {
                    using var r = e.OpenReader();
                    r.CopyTo(outStream);
                });
                newEntry.LastModified = e.LastModified;
            }

            dst.Save(outMs);
            return outMs.ToArray();
        }

        /// <summary>
        /// Normalizes the given zip file, removing all extra metadata.
        /// </summary>
        /// <param name="input">The zip file to normalize.</param>
        /// <returns>The normalized zip file.</returns>
        public static byte[] NormalizeZipBcl(byte[] input)
        {
            using var srcMs = new MemoryStream(input);
            using var src = new ZipArchive(srcMs, ZipArchiveMode.Read, leaveOpen: true,
                entryNameEncoding: Encoding.UTF8);

            using var outMs = new MemoryStream();
            using (var dst = new ZipArchive(outMs, ZipArchiveMode.Create, leaveOpen: true,
                       entryNameEncoding: Encoding.UTF8))
            {
                var fixedTime = new DateTimeOffset(2000, 1, 1, 0, 0, 0, TimeSpan.Zero);

                foreach (var e in src.Entries
                             .Where(e => e.Length >= 0) // skip directories
                             .Where(e => !ShouldSkip(e.FullName))
                             .OrderBy(e => NormalizePath(e.FullName), StringComparer.Ordinal))
                {
                    // Read bytes
                    byte[] data;
                    using (var es = e.Open())
                    using (var buf = new MemoryStream())
                    {
                        es.CopyTo(buf);
                        data = buf.ToArray();
                    }

                    var name = NormalizePath(e.FullName);
                    var ne = dst.CreateEntry(name, CompressionLevel.SmallestSize);
                    ne.LastWriteTime = fixedTime; // fixed timestamp for determinism

                    using var s = ne.Open();
                    s.Write(data, 0, data.Length);
                }
            }

            return outMs.ToArray();
        }

        private static bool ShouldSkip(string path)
            => path.StartsWith("__MACOSX/", StringComparison.Ordinal) ||
               path.Split('/').Last().StartsWith("._", StringComparison.Ordinal);

        private static string NormalizePath(string path)
        {
            var p = path.Replace('\\', '/');
            if (p.StartsWith("./", StringComparison.Ordinal))
            {
                p = p[2..];
            }

            return p;
        }

        private static List<string> DiscoverAllFilesMatchingPattern(string workingDirectory, string pattern)
        {
            var files = new List<string>(
                Directory.GetFiles(
                    workingDirectory,
                    pattern,
                    SearchOption.AllDirectories));

            if (files.Count == 0)
            {
                throw new SolutionException($"A file following the pattern '{pattern}' was not found in the submission!");
            }

            return files;
        }
    }
}
