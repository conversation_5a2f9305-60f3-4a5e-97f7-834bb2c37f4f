<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\Common\OJS.Common\OJS.Common.csproj" />
    <ProjectReference Include="..\..\Common\OJS.Services.Common\OJS.Services.Common.csproj" />
    <ProjectReference Include="..\..\Common\OJS.Workers\OJS.Workers.Checkers\OJS.Workers.Checkers.csproj" />
    <ProjectReference Include="..\..\Common\OJS.Workers\OJS.Workers.Common\OJS.Workers.Common.csproj" />
    <ProjectReference Include="..\..\Common\OJS.Workers\OJS.Workers.ExecutionStrategies\OJS.Workers.ExecutionStrategies.csproj" />
    <ProjectReference Include="..\..\Common\OJS.Workers\OJS.Workers.Compilers\OJS.Workers.Compilers.csproj" />
    <ProjectReference Include="..\OJS.Services.Worker.Models\OJS.Services.Worker.Models.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.2" />
    <PackageReference Include="Serilog" Version="4.0.2" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
  </ItemGroup>
</Project>