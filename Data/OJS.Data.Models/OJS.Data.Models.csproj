<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Common\OJS.Common\OJS.Common.csproj" />
    <ProjectReference Include="..\..\Services\Common\OJS.Workers\OJS.Workers.Common\OJS.Workers.Common.csproj" />
    <ProjectReference Include="..\..\Services\Infrastructure\OJS.Services.Infrastructure\OJS.Services.Infrastructure.csproj" />
    <ProjectReference Include="..\OJS.Data.Validation\OJS.Data.Validation.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.18" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.18" />
    <PackageReference Include="Microsoft.Extensions.Identity.Stores" Version="8.0.18" />
  </ItemGroup>
</Project>