namespace OJS.Servers.Infrastructure.Extensions;

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using OJS.Services.Common.Models;
using System.Diagnostics;
using static OJS.Common.Constants.ServiceConstants;

public static class ControllerResultExtensions
{
    public static IActionResult ToActionResult<T>(this ServiceResult<T> result, HttpContext httpContext)
    {
        if (result.IsSuccess)
        {
            return new OkObjectResult(result.Data);
        }

        Activity.Current?.SetStatus(ActivityStatusCode.Error, result.ErrorMessage);
        Activity.Current?.SetTag("http.instance_id", result.InstanceId);
        Activity.Current?.SetTag("http.status_error_code", result.ErrorCode);
        Activity.Current?.SetTag("http.status_error_context", result.ErrorContext);

        var logger = httpContext.RequestServices.GetRequiredService<ILogger<ServiceResult<T>>>();
        var problemDetails = new ProblemDetails
        {
            Detail = result.ErrorMessage,
            Instance = result.InstanceId,
            Extensions =
            {
                [nameof(result.ErrorContext)] = result.ErrorContext,
                [nameof(result.ErrorCode)] = result.ErrorCode,
            },
        };

        IActionResult actionResult;

        switch (result.ErrorCode)
        {
            case ErrorCodes.AccessDenied:
                actionResult = new ForbidResult();
                break;
            case ErrorCodes.NotFound:
                problemDetails.Title = "Not found";
                problemDetails.Status = StatusCodes.Status404NotFound;
                actionResult = new NotFoundObjectResult(problemDetails);
                logger.LogWarning("Not found with instance {InstanceId}: {Message}", result.InstanceId, result.ErrorMessage);
                break;
            case ErrorCodes.BusinessRuleViolation:
                problemDetails.Title = "Business rule violation";
                problemDetails.Status = StatusCodes.Status422UnprocessableEntity;
                actionResult = new BadRequestObjectResult(problemDetails)
                {
                    StatusCode = StatusCodes.Status422UnprocessableEntity,
                };
                logger.LogWarning("Business rule violation with instance {InstanceId}: {Message}. Context: {ErrorContext}", result.InstanceId, result.ErrorMessage);
                break;
            default:
                problemDetails.Title = "Internal server error";
                problemDetails.Status = StatusCodes.Status500InternalServerError;
                actionResult = new ObjectResult(problemDetails)
                {
                    StatusCode = StatusCodes.Status500InternalServerError,
                };
                logger.LogError("Internal server error with instance {InstanceId}: {Message}", result.InstanceId, result.ErrorMessage);
                break;
        }


        return actionResult;
    }
}