namespace OJS.Servers.Infrastructure.Extensions;

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using OJS.Services.Common.Models;
using OJS.Services.Infrastructure.Constants;
using System.Diagnostics;
using static OJS.Common.Constants.ServiceConstants;

public static class ControllerResultExtensions
{
    public static IActionResult ToActionResult<T>(this ServiceResult<T> result, HttpContext httpContext)
    {
        if (result.IsSuccess)
        {
            return new OkObjectResult(result.Data);
        }

        Activity.Current?.SetStatus(ActivityStatusCode.Error, result.ErrorMessage);
        Activity.Current?.SetTag("service_result.instance_id", result.InstanceId);
        Activity.Current?.SetTag("service_result.error_code", result.ErrorCode);
        Activity.Current?.SetTag("service_result.error_context", result.ErrorContext);

        var traceId = Activity.Current?.TraceId.ToString();

        var logger = httpContext.RequestServices.GetRequiredService<ILogger<ServiceResult<T>>>();
        var problemDetails = new ProblemDetails
        {
            Detail = result.ErrorMessage,
            Instance = traceId ?? result.InstanceId,
            Extensions =
            {
                [nameof(result.ErrorContext)] = result.ErrorContext,
                [nameof(result.ErrorCode)] = result.ErrorCode,
                [nameof(result.InstanceId)] = result.InstanceId,
            },
        };

        IActionResult actionResult;

        switch (result.ErrorCode)
        {
            case ErrorCodes.AccessDenied:
                actionResult = new ForbidResult();
                logger.LogServiceResultAccessDenied(result.InstanceId, result.ErrorMessage, result.ErrorCode, new { result.ErrorContext, traceId });
                break;
            case ErrorCodes.NotFound:
                problemDetails.Title = "Not found";
                problemDetails.Status = StatusCodes.Status404NotFound;
                actionResult = new NotFoundObjectResult(problemDetails);
                logger.LogServiceResultNotFound(result.InstanceId, result.ErrorMessage, result.ErrorCode, new { result.ErrorContext, traceId });
                break;
            case ErrorCodes.BusinessRuleViolation:
                problemDetails.Title = "Business rule violation";
                problemDetails.Status = StatusCodes.Status422UnprocessableEntity;
                actionResult = new BadRequestObjectResult(problemDetails)
                {
                    StatusCode = StatusCodes.Status422UnprocessableEntity,
                };
                logger.LogServiceResultBusinessRuleViolation(result.InstanceId, result.ErrorMessage, result.ErrorCode, new { result.ErrorContext, traceId });
                break;
            default:
                problemDetails.Title = "Internal server error";
                problemDetails.Status = StatusCodes.Status500InternalServerError;
                actionResult = new ObjectResult(problemDetails)
                {
                    StatusCode = StatusCodes.Status500InternalServerError,
                };
                logger.LogServiceResultInternalServerError(result.InstanceId, result.ErrorMessage, result.ErrorCode, new { result.ErrorContext, traceId });
                break;
        }

        return actionResult;
    }
}