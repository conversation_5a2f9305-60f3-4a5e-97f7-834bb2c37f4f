{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:8003", "sslPort": 44333}}, "profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "OJS.Servers.Worker": {"commandName": "Project", "dotnetRunMessages": "true", "launchBrowser": false, "applicationUrl": "http://localhost:8003", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}