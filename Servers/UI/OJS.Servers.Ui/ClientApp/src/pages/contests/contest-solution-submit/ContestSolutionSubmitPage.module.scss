/* stylelint-disable selector-max-compound-selectors, no-descending-specificity */

@use 'src/styles/fonts';
@use 'src/styles/colors';
@use 'src/styles/variables';
@use 'src/styles/spacings';
@use 'src/styles/shadows';
@use 'src/styles/font-weights';

.contestSolutionSubmitWrapper {
  @extend %font-family-normal;

  .title {
    font-size: variables.$f-size-30;
    font-weight: font-weights.$font-weight-bold;
  }

  .nameWrapper {
    align-items: center;
    display: flex;
    justify-content: space-between;
    margin-bottom: spacings.$sp-32;
    margin-top: spacings.$sp-32;

    .contestNameAndAdminButtons {
      align-items: center;
      display: flex;
      flex-direction: row;
    }

    .allResultsLink {
      color: colors.$secondary-green;
      cursor: pointer;
      text-decoration: underline;
    }
  }

  .problemsAndEditorWrapper {
    display: flex;
    margin-top: spacings.$sp-18;
  }

  .submissionsWrapper {
    display: flex;
    flex-direction: column;
    margin-top: spacings.$sp-24;

    .submissionsTitleWrapper {
      align-items: center;
      display: flex;
      justify-content: flex-start;
      margin-bottom: spacings.$sp-32;

      svg {
        cursor: pointer;
        margin-left: spacings.$sp-12;
      }
    }

    > div {
      font-weight: font-weights.$font-weight-semi-bold;
      margin-bottom: spacings.$sp-16;
    }
  }

  @keyframes rotate {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  .rotate {
    animation: rotate 1s linear infinite;
  }
}

.problemDetailsWrapper {
  display: flex;
  justify-content: space-between;
  margin: spacings.$sp-8 0;
}

.adminButtonsContainer {
  display: flex;
  gap: spacings.$sp-12;
  margin-left: spacings.$sp-12;
}

.problemResources {
  display: flex;
}

.selectedProblemWrapper {
  margin-left: spacings.$sp-24;
  width: 90%;

  .problemNameAndTimeWrapper {
    align-items: center;
    color: colors.$primary-blue;
    display: flex;
    justify-content: space-between;

    b {
      font-weight: font-weights.$font-weight-bold;
      margin-left: spacings.$sp-8;
    }

    .problemName {
      align-items: center;
      display: flex;
      font-size: variables.$f-size-30;
      font-weight: font-weights.$font-weight-bold;

      span {
        font-size: variables.$f-size-16;
        font-weight: font-weights.$font-weight-light;
        margin-left: spacings.$sp-12;
      }
    }
  }

  .problemParametersWrapper {
    align-items: center;
    display: flex;
    justify-content: space-between;

    > svg {
      color: colors.$primary-blue;
      cursor: pointer;
    }
  }
}

.submitSettings {
  align-items: flex-start;
  display: flex;
  justify-content: flex-end;
  margin-top: spacings.$sp-24;

    > :first-child {
        margin-right: auto;
    }
}

.fileUpload {
  div {
    margin: spacings.$sp-12 0;

    span {
      font-weight: font-weights.$font-weight-semi-bold;
    }
  }
}

.fileUploadDropdown {
  div {
    margin: 0 !important;
  }
}

.fileUploadError {
  color: colors.$primary-red;
}

.popoverContent {
  @extend %font-family-normal;

  font-size: variables.$f-size-14;
  overflow: hidden;
  padding: 0 spacings.$sp-8;

  div {
    margin: spacings.$sp-8 0;
  }

  .title {
    font-weight: font-weights.$font-weight-semi-bold;
  }
}

.remainingTimeNadSubmitButtonWrapper {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  justify-content: end;
  margin-left: spacings.$sp-24;

  > div {
    margin-bottom: spacings.$sp-12;
  }

  .button {
    width: 200px;
  }
}

.solutionSubmitError {
  color: colors.$primary-red;
  font-size: variables.$f-size-18;
  font-weight: font-weights.$font-weight-semi-bold;
  margin-top: spacings.$sp-18;
  text-align: end;
}

.errorText {
  color: colors.$primary-red;
}

.remainingTimeWrapper {
  color: colors.$dark-base-color-100;
  margin-top: spacings.$sp-12;
}

.disabledSubmissionsRefreshButton {
  opacity: 0.5;
  pointer-events: none;
}

.contestResourcesWrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}
