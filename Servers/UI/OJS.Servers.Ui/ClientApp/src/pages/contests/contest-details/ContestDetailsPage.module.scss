@use 'src/styles/fonts';
@use 'src/styles/variables';
@use 'src/styles/spacings';
@use 'src/styles/font-weights';
@use 'src/styles/colors';

.contestDetailsWrapper {
  @extend %font-family-normal;

  .heading {
    @extend %font-family-normal;

    border-bottom-width: 0;
    font-weight: font-weights.$font-weight-semi-bold;
    padding: spacings.$sp-32 0;
  }

  .allowedLanguageLink {
    color: colors.$primary-blue;

    &:hover {
      text-decoration: underline;
    }
  }

  .problemNameItem {
    font-size: variables.$f-size-18;
    line-height: 1.8;
  }

  .problemResources {
    line-height: 1.5;
  }
}

.administrationButtonsWrapper {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.descriptionBoxWrapper {
  display: flex;
  font-size: variables.$f-size-18;
  justify-content: space-between;
  line-height: 1.2;
  margin-top: spacings.$sp-24;

  .title {
    font-size: variables.$f-size-24;
    font-weight: font-weights.$font-weight-bold;
    margin-bottom: spacings.$sp-18;
  }

  .languagesWrapper {
    line-height: 1.5;
    margin: spacings.$sp-24 0;
  }

  .contestResourcesWrapper {
    display: flex;
    justify-content: left;
    align-items: center;
    line-height: 1.5;
    margin: spacings.$sp-24 0;
  }

  .allowedLanguages {
    font-size: variables.$f-size-18;
    font-weight: font-weights.$font-weight-bold;
  }

  > div {
    width: 50%;

    > div {
      margin-bottom: spacings.$sp-4;
    }

    // contest details
    &:nth-child(1) {
      margin-right: spacings.$sp-64;
    }

    // contest problems
    &:nth-child(2) {
      margin-left: spacings.$sp-64;
    }
  }
}

.actionBtnWrapper {
  align-items: center;
  color: colors.$primary-blue;
  display: flex;
  margin: spacings.$sp-16 0;

  > button {
    margin-right: spacings.$sp-12;
  }

  > a {
    align-items: center;
    display: flex;

    > svg {
      margin: 0 spacings.$sp-8;
    }

    .underlinedBtnText {
      text-decoration: underline;
    }

    &:visited {
      color: inherit;
    }
  }

  .greenColor {
    color: colors.$secondary-green;

    &:visited {
      color: colors.$secondary-green;
    }
  }

  .blueColor {
    color: inherit;

    &:visited {
      color: inherit;
    }
  }

  .resultsTextContainer {
    display: flex;
    flex-direction: row;
    gap: 5px;
  }
}

.problemResourceWrapper {
  display: flex;
  margin-right: 12px;
}
