@use 'src/styles/colors.scss';

.lightTheme {
  .node {
    align-items: center;
    border-radius: 6px;
    color: #333;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
    margin-left: 0;
    transition: background-color 0.3s ease, color 0.3s ease;

    &:hover {
      background-color: #e6e6e6;
    }
  }

  .cardContainer {
    align-items: center;
    display: flex;
    justify-content: space-between;
  }

  .icon {
    transition: transform 0.2s ease;
  }

  .iconOpen {
    transform: rotate(90deg);
  }

  .iconClosed {
    transform: rotate(0deg);
  }

  .nodeName {
    font-size: 18px;
    font-weight: 500;
    margin-left: 4px;
  }
}

.darkTheme {
  .node {
    align-items: center;
    border-radius: 6px;
    color: #f0f0f0;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    transition: background-color 0.3s ease, color 0.3s ease;

    &:hover {
      background-color: #444;
    }
  }

  .cardContainer {
    align-items: center;
    display: flex;
    justify-content: center;
  }

  .icon {
    display: inline-block;
    transition: transform 0.2s ease;
  }

  .iconOpen {
    transform: rotate(90deg);
  }

  .iconClosed {
    transform: rotate(0deg);
  }

  .nodeName {
    font-size: 18px;
    font-weight: 500;
    margin-left: 4px;
  }
}

.treeContainer {
  // Added to remove the container's horizontal scrollbar
  max-width: 100px;
}

.tree {
  :global {
    max-height: 80vh;
  }
}

.hidden {
  visibility: hidden;
}

.loaderContainer {
  align-items: center;
  display: flex;
  height: 100vh;
  justify-content: center;
  left: 0;
  position: fixed;
  top: 0;
  width: 100vw;
}

.notFoundContainer {
  align-items: center;
  display: flex;
  height: 100vh;
  justify-content: center;
  left: 0;
  position: fixed;
  top: 0;
  width: 100vw;
}

.lightRed {
  color: #f8bbd0;
}

.administrationModal {
  border: none !important;
  outline: none !important;
}

.iconButton {
  &:hover {
    background-color: rgb(68 169 248 / 10%) !important;
  }

  .MuiSvgIcon-root {
    color: inherit !important;
  }
}
