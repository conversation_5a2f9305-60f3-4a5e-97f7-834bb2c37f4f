@use 'src/styles/colors';
@use 'src/styles/variables';
@use 'src/styles/fonts';
@use 'src/styles/shadows';
@use 'src/styles/spacings';

.tableContainer {
  overflow-x: auto;
}

.contestResultsGrid {
  border-collapse: collapse;
  color: colors.$white-color;
  max-width: 1680px;
  min-width: 100%;
  overflow-x: auto;
  table-layout: auto;

  th,
  td {
    overflow: hidden;
    padding: spacings.$sp-16;
    text-align: center;
    text-overflow: ellipsis;
    vertical-align: middle;
  }

  .contestResultsGridHeader {
    @extend %font-family-normal;
  }

  .lightContestResultsGridHeader {
    background: colors.$primary-blue;
    box-shadow: shadows.$dp-shadow-1;
    color: colors.$light-base-color-100 !important;
  }

  .darkContestResultsGridHeader {
    background: colors.$dark-base-color-500;
    box-shadow: shadows.$dp-shadow-4;
  }
}

.row {
  @extend %font-family-normal;

  height: 100%;
}

.lightRow {
  background: colors.$light-base-color-100;
  border-bottom: 1px solid colors.$light-base-color-200;
}

.darkRow {
  background: colors.$primary-dark-grey-color;
  border-bottom: 1px solid colors.$dark-base-color-400;
}

.resultLink {
  color: inherit;

  &:hover {
    text-decoration: underline;
  }
}

.userRow {
  background: colors.$primary-blue;
  color: colors.$light-base-color-100 !important;
}

.problemLink {
  color: inherit;
  text-decoration: none;
  transition: all 0.2s ease-in-out;

  &:hover {
    text-decoration: underline;
  }
}
