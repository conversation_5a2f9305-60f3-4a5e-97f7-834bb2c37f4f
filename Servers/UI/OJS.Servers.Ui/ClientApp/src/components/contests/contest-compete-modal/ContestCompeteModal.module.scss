@use 'src/styles/colors';
@use 'src/styles/variables';
@use 'src/styles/fonts';
@use 'src/styles/spacings';
@use 'src/styles/font-weights';
@use 'src/styles/components';
@use 'src/styles/border-radiuses';

.modalWrapper {
  @extend %font-family-normal;

  align-self: center;
  font-size: variables.$f-size-18;
  margin-top: spacings.$sp-64;
  width: 80%;

  .modalTitle {
    background-color: colors.$primary-blue;
    border-top-left-radius: border-radiuses.$br-6;
    border-top-right-radius: border-radiuses.$br-6;
    color: colors.$white-color;
    padding: spacings.$sp-24 spacings.$sp-18;
  }

  .modalBody {
    border-bottom-left-radius: border-radiuses.$br-6;
    border-bottom-right-radius: border-radiuses.$br-6;
    line-height: 1.2;
    padding: spacings.$sp-18;

    div {
      margin: spacings.$sp-24 0;
    }

    .competeText {
      color: colors.$secondary-green;
    }
  }

  .buttonsWrapper {
    align-self: center;
    display: flex;
    justify-content: center;
    margin-top: spacings.$sp-32 !important;

    button {
      margin: 0 spacings.$sp-12;
    }

    .contestButton {
      @extend %medium;
    }
  }

  b {
    font-weight: font-weights.$font-weight-bold;
  }
}
