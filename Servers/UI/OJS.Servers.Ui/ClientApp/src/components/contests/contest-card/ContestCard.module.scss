@use 'src/styles/shadows';
@use 'src/styles/fonts';
@use 'src/styles/spacings';
@use 'src/styles/border-radiuses';
@use 'src/styles/variables';
@use 'src/styles/colors';
@use 'src/styles/font-weights';

.contestCardWrapper {
  @extend %font-family-normal;

  align-items: center;
  border-radius: border-radiuses.$br-6;
  box-shadow: shadows.$dp-shadow-1;
  display: flex;
  font-size: variables.$f-size-12;
  justify-content: space-between;
  margin: spacings.$sp-8 0;
  padding: spacings.$sp-16 spacings.$sp-4 spacings.$sp-16 spacings.$sp-18;

  .contestDetailsFragmentsWrapper {
    display: flex;
    flex-wrap: wrap;
  }

  .contestCardTitle {
    color: inherit;
    font-size: variables.$f-size-18;
    font-weight: font-weights.$font-weight-regular;

    &:hover {
      text-decoration: underline;
    }
  }

  .contestCardSubTitle {
    margin-top: spacings.$sp-4;
  }

  .contestDetailsFragment {
    color: colors.$primary-blue;
    display: flex;
    justify-content: space-between;
    margin: spacings.$sp-16 spacings.$sp-16 0 0;

    .icon {
      font-size: variables.$f-size-12;
      margin-right: spacings.$sp-4;
    }
  }

  .greenColor {
    color: colors.$secondary-green;
  }

  .hasUnderLine {
    cursor: pointer;
    text-decoration: underline;
  }

  .competeLock {
    color: colors.$secondary-green;
  }

  .practiceLock {
    color: colors.$light-blue-color;
  }

  .hideLock {
    visibility: hidden;
  }

  .lockFaint {
    opacity: 0.5;
  }
}

.actionsWrapper {
  align-content: center;
  display: flex;
  gap: 0.7rem;

  .actionsContainer {
    align-self: center;
    display: flex;
    font-size: 20px;
    gap: 0.7rem;

    &.lightTheme {
      .icon {
        color: colors.$light-text-color;
      }
    }

    &.darkTheme {
      .icon {
        color: colors.$dark-text-color;
      }
    }
  }
}

.contestBtnsWrapper {
  display: flex;
  font-size: variables.$f-size-16;
  margin-left: spacings.$sp-12;

  .buttonAndPointsLabelWrapper {
    align-items: center;
    display: flex;
    gap: spacings.$sp-12;
    justify-content: flex-end;

    .points {
      flex: 1;
      text-align: right;
    }

    &:nth-child(2) {
      margin-top: spacings.$sp-12;
    }
  }

  .buttonAndLockLabelWrapper {
    align-items: center;
    display: flex;
    gap: spacings.$sp-4;
    justify-content: flex-end;
  }
}
