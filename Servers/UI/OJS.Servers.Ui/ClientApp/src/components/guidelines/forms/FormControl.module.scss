@use 'src/styles/colors';
@use 'src/styles/fonts';
@use 'src/styles/font-weights';
@use 'src/components/guidelines/buttons/Button.module';
@use 'src/styles/spacings';

%lightBackground {
  background-color: colors.$white-color;
}

%darkBackground {
  background-color: colors.$dark-base-color-200;
}

input:focus,
select:focus,
textarea:focus,
button:focus {
  outline: none;
}

.formControlContainer {
  margin-top: 10px;
  position: relative;
}

.lightFormControl {
  @extend %lightBackground;

  border: 1px solid colors.$dark-base-color-200;
}

.darkFormControl {
  @extend %darkBackground;

  border: 1px solid colors.$white-color;
}

.formControl {
  @extend %font-family-normal;

  border-radius: 6px;
  box-sizing: border-box;
  font-size: 16px;
  margin-top: 15px;
  min-height: 40px;
  padding: 16px;
  position: relative;
  width: 100%;

  &:focus + label,
  &:not(:placeholder-shown) + label {
    padding: spacings.$sp-8;
    transform: translateX(-13%) translateY(-130%) scale(0.8);
  }

  &[type='text'],
  &[type='password'],
  &.formControlTextArea {
    border-radius: 6px;
    color: colors.$grey-color;
    display: block;
    font-weight: font-weights.$font-weight-light;
    max-height: 50px;
    outline: none;
    padding: 16px;
    width: 100%;
  }

  &[type='checkbox'],
  &[type='radio'] {
    appearance: none;
    border-radius: 3px;
    cursor: pointer;
    height: 18px;
    padding: 0;
    width: 18px;

    &::after {
      background: url('./check-white.svg') no-repeat 35%;
      background-size: 70%;
      border-radius: 2px;
      content: '';
      height: 16px;
      left: 0;
      padding: 0;
      position: absolute;
      text-align: center;
      top: 0;
      transition: all 0.2s ease-in;
      width: 16px;
    }

    &:checked {
      border-color: colors.$primary-blue;

      &::after {
        background-color: colors.$primary-blue;
        transform: scale(1);
      }
    }

    &:not(:checked) {
      &::after {
        transform: scale(0);
      }
    }
  }
}

.formLabel {
  background: none;
  display: block;
  margin-left: 15px;
  position: absolute;
  top: 60%;
}

.lightInputPlaceholder {
  &::placeholder {
    color: colors.$light-text-color;
    opacity: 1;
  }
}

.darkInputPlaceholder {
  &::placeholder {
    color: colors.$dark-text-color;
    opacity: 1;
  }
}

.fileInput {
  border: 0;
  padding: 5px;
}

input::file-selector-button {
  @extend %btn;

  color: colors.$color-btn-secondary;
  margin-right: 10px;
}

.inputPasswordWrapper {
  position: relative;

  .passwordIconWrapper {
    position: absolute;
    right: 10px;
    top: 17px;

    .passwordIcon {
      cursor: pointer;
      font-size: 18px;
    }
  }
}
