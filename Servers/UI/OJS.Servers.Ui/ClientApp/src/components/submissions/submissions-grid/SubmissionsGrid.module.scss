@use 'src/styles/colors';
@use 'src/styles/variables';
@use 'src/styles/fonts';
@use 'src/styles/shadows';
@use 'src/styles/spacings';

.submissionsGrid {
  border-collapse: collapse;
  color: colors.$white-color;
  table-layout: auto;
  width: 100%;

  .header {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 0.2rem;
    min-width: 100%;
    padding-right: 8px;
  }

  td {
    height: 65px;
    padding: spacings.$sp-12;
    text-align: left;
    vertical-align: middle;
    white-space: normal;
    word-wrap: break-word;
  }

  // ID column
  td:nth-child(1) {
    min-width: 80px;
  }

  // Task column
  td:nth-child(2) {
    min-width: 150px;
    white-space: normal;
  }

  // From column (date)
  td:nth-child(3) {
    min-width: 150px;
  }

  // Time and Memory Used column
  td:nth-child(5) {
    min-width: 200px;
  }

  // Result column
  td:nth-child(6) {
    min-width: 150px;
  }

  // Strategy column
  td:nth-child(7) {
    min-width: 200px;
  }

  // Action column
  td:last-child {
    min-width: 60px;
    width: 1%;
  }

  .submissionsGridHeader {
    @extend %font-family-normal;
  }

  .lightSubmissionsGridHeader {
    background: colors.$primary-blue;
    box-shadow: shadows.$dp-shadow-1;
    color: colors.$light-base-color-100 !important;
  }

  .darkSubmissionsGridHeader {
    background: colors.$dark-base-color-500;
    box-shadow: shadows.$dp-shadow-4;
  }
}

.noSubmissionsRow {
  td {
    padding: 0 !important;
  }
}

.noSubmissionsContainer {
  align-items: center;
  display: flex;
  font-size: variables.$f-size-20;
  height: 300px;
  justify-content: center;
  text-align: center;
  width: 100%;
}

:global(.columnFiltersContainer) {
  min-width: 24px;
}
