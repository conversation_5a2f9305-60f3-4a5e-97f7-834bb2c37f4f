@use 'src/styles/colors';
@use 'src/styles/fonts';
@use 'src/styles/variables';
@use 'src/styles/responsive';
@use 'src/styles/shadows';
@use 'src/styles/spacings';
@use 'src/styles/font-weights.scss';
@use 'src/styles/border-radiuses.scss';

.row {
  @extend %font-family-normal;
}

.lightRow {
  // TODO: Fix this color
  background: colors.$light-base-color-100;
  border-bottom: 1px solid colors.$light-base-color-200;
}

.darkRow {
  background: colors.$primary-dark-grey-color;
  border-bottom: 1px solid colors.$dark-base-color-400;
}

.redirectButton {
  background-color: inherit;
  border: 0;
  color: colors.$light-blue-color;
  cursor: pointer;
  font: inherit;
  font-size: variables.$f-size-16;
  margin-top: spacings.$sp-4;
  padding-left: 0;
  text-align: left;
}

.link {
  @extend %r-heading-primary;

  color: colors.$light-blue-color;
  display: inline-block;
  margin: spacings.$sp-8;
}

.strategy {
  align-items: center;
  display: flex;
  flex-direction: row;
  gap: spacings.$sp-12;
  height: 100%;
  justify-content: start;
}

.competeIcon {
  margin: 0 spacings.$sp-12;
}

.executionResultContainer {
  align-items: center;
  display: flex;
  justify-content: flex-start;
}

.maxTimeUsed,
.maxMemoryUsed {
  align-items: center;
  display: flex;
  flex-direction: column;

  .memoryIcon,
  .timeIcon {
    color: colors.$dark-base-color-100;
  }

  .timeAndMemoryText {
    font-size: variables.$f-size-16 !important;
    margin-top: spacings.$sp-4;
  }
}

.competeIconModal {
  @extend %font-family-normal;

  padding: spacings.$sp-8 spacings.$sp-18;
}

.timeAndMemoryContainer {
  align-items: center;
  display: flex;
  gap: spacings.$sp-12;
  justify-content: flex-start;
}

.strategyWrapper {
  align-items: center;
  display: flex;
  justify-content: flex-start;

  span {
    margin-left: spacings.$sp-12;
  }
}

.fromDate {
  align-items: start;
  display: flex;
  flex-direction: column;
  justify-content: start;

  .fromDateRow {
    align-items: center;
    display: flex;
    gap: 3px;
    justify-content: center;

    .icon {
      font-size: 17px !important;
    }
  }
}
