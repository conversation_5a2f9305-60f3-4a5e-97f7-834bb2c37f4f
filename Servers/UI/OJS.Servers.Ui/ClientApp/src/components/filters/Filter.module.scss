@use 'src/styles/colors.scss';
@use 'src/styles/spacings';

.popupContainer {
  border: 2px solid colors.$background-color-btn-primary;
  border-radius: 12px;
  box-shadow: colors.$box-shadow-color;
  min-width: 320px;
  opacity: 0;
  padding: spacings.$sp-24;
  pointer-events: none;
  transform: scale(0.95);
  transform-origin: top left;
  width: auto;
  z-index: 1300;

  &.open {
    opacity: 1;
    pointer-events: auto;
    transform: scale(1);
  }

  & .closeIcon {
    cursor: pointer;
    position: absolute;
    right: 12px;
    top: 12px;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.1);
    }
  }

  & .fieldsContainer {
    align-items: center;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    justify-content: center;
    margin-top: 1.5rem !important;
    padding: 0.5rem;

    & .title {
      align-items: center;
      border-bottom: 1px solid rgb(colors.$background-color-btn-primary, 0.1);
      display: flex;
      font-size: 1.2rem;
      font-weight: 600;
      justify-content: space-between;
      padding-bottom: 0.5rem;
      width: 100%;
    }

    & .hidden {
      visibility: hidden;
    }

    & .removeFilterButton {
      cursor: pointer;
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.1);
      }
    }

    :global {
      .MuiInputLabel-root {
        color: colors.$background-color-btn-primary;
        font-size: 0.9rem;
        transform: translate(0, -1.5px) scale(0.85);
      }

      .MuiInput-underline::before {
        border-bottom-color: colors.$background-color-btn-primary;
      }

      .MuiInput-underline:hover::before {
        border-bottom-color: colors.$background-color-btn-primary;
      }

      .MuiInput-underline::after {
        border-bottom-color: colors.$background-color-btn-primary;
      }

      .Mui-disabled {
        & .MuiInput-underline::before {
          border-bottom-color: colors.$background-color-btn-primary;
          border-bottom-style: solid;
          opacity: 0.6;
        }
      }

      .MuiSelect-select {
        padding: 8px 0;
      }

      .MuiFormControl-root {
        margin: 8px 0;
      }
    }
  }

  & .buttonsSection {
    border-top: 1px solid rgb(colors.$background-color-btn-primary, 0.1);
    display: flex;
    justify-content: space-between;
    margin-top: spacings.$sp-24;
    padding-top: spacings.$sp-16;

    button {
      border-radius: 6px;
      min-width: 120px;
      padding: 8px 16px;
      transition: all 0.2s ease;

      &:hover:not(:disabled) {
        transform: translateY(-1px);
      }

      &:disabled {
        cursor: not-allowed;
        opacity: 0.6;
      }
    }
  }

  & .removeAllFilters {
    color: colors.$primary-red;

    &:hover {
      background-color: rgba(colors.$primary-red, 0.1);
    }
  }

  & .addFilter {
    background-color: rgba(colors.$background-color-btn-primary, 0.1);

    &:hover:not(:disabled) {
      background-color: rgba(colors.$background-color-btn-primary, 0.2);
    }
  }

  &.lightTheme {
    background-color: colors.$white-color;
    color: colors.$primary-dark-grey-color;

    :global {
      .MuiInputBase-input {
        color: colors.$primary-dark-grey-color !important;
      }

      .MuiInputBase-input.Mui-disabled {
        color: colors.$primary-dark-grey-color !important;
        opacity: 0.7;
        -webkit-text-fill-color: colors.$primary-dark-grey-color !important;
      }

      .MuiInputLabel-root {
        color: colors.$primary-dark-grey-color;
      }

      .MuiInput-underline::before {
        border-bottom-color: colors.$light-base-color-500;
      }

      .MuiInput-underline:hover::before {
        border-bottom-color: colors.$primary-blue;
      }

      .MuiInput-underline::after {
        border-bottom-color: colors.$primary-blue;
      }
    }

    & .addFilter {
      background-color: colors.$light-base-color-200;
      color: colors.$primary-dark-grey-color;

      &:hover:not(:disabled) {
        background-color: colors.$light-base-color-300;
      }

      &:disabled {
        background-color: colors.$light-base-color-200;
        opacity: 0.5;
      }
    }

    .MuiSvgIcon-root {
      color: colors.$primary-dark-grey-color;
    }

    .option {
      background-color: colors.$white-color;
      color: colors.$primary-dark-grey-color;

      &:hover,
      &[aria-selected='true'] {
        background-color: colors.$light-base-color-200 !important;
      }
    }

    & .title {
      border-bottom-color: colors.$light-base-color-300;
      color: colors.$primary-dark-grey-color;
    }

    & .buttonsSection {
      border-top-color: colors.$light-base-color-300;
    }
  }

  &.darkTheme {
    background-color: colors.$dark-base-color-400;
    color: colors.$dark-text-color;

    :global {
      .MuiInputBase-input.Mui-disabled {
        color: colors.$dark-text-color !important;
        -webkit-text-fill-color: colors.$dark-text-color !important;
      }
    }

    & .addFilter {
      color: colors.$dark-text-color;

      &:disabled {
        opacity: 0.6;
      }
    }

    .MuiSvgIcon-root {
      color: colors.$dark-text-color;
    }

    .option {
      background-color: colors.$dark-base-color-400;
      color: colors.$dark-text-color;

      &:hover,
      &[aria-selected='true'] {
        background-color: colors.$dark-base-color-300 !important;
      }
    }
  }
}

.divider {
  background-color: rgb(colors.$background-color-btn-primary, 0.1) !important;
  border: 0;
  border-radius: 1rem;
  height: 1px;
  margin: 16px 0;
  width: 100%;
}

.input {
  font-size: spacings.$sp-16;
}

.textField {
  margin: 8px 0;
  width: 100%;
}
