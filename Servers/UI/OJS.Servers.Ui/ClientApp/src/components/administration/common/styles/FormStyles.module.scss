.buttonsWrapper {
  align-self: center;
  display: flex;
  justify-content: space-around;
  padding: 1rem;
  width: 30%;
}

.button {
  height: 3rem;
  margin: 0 10px;
  width: 10rem;
}

.row {
  display: flex;
  gap: 1rem;
  justify-content: space-between;
  width: 90%;
}

.inlineElement {
  width: 50%;
}

.centralize {
  align-self: center;
  margin-bottom: 10px !important;
  text-align: center;
}

.left {
  align-self: start;
  margin-bottom: 10px !important;
  text-align: left;
}

.inputRow {
  margin-bottom: 0.5rem !important;
  margin-top: 0.5rem !important;
  width: 90%;
}

.form {
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  margin: 0 auto;
  min-height: 400px;
  width: 100%;

  :global {
    .MuiFormLabel-root {
      font-size: 20px;
    }
  }
}

.dividerLabel {
  align-self: flex-start;
  margin-top: 1rem !important;
}

.spacing {
  margin: 1rem !important;
}

.selectFormGroup {
  align-self: center;
  margin: 1rem 0;
  width: 90%;
}

.fieldBox {
  align-items: start;
  display: flex;
  flex-direction: column;
  height: auto;
  width: 90%;

  .fieldBoxTitle {
    align-self: flex-start;
    font-weight: bold;
    margin-bottom: 0.5rem;
    margin-top: 1rem;
    width: 100%;
  }

  .fieldBoxDivider {
    border-top: 1px solid #ccc;
    margin-bottom: 1rem;
    width: 100%;
  }

  .fieldBoxElement {
    align-items: center;
    display: flex;
    flex-direction: column;
    margin: 0 auto;
    width: 90%;

    .inputRow {
      width: 100%;
    }
  }

  .fieldBoxCheckBoxes {
    display: flex;
    flex-direction: column;
    margin: 20px 0;
    width: 90%;
  }

  .fieldBoxElementLeft {
    align-self: start;
    width: 50%;
  }

  .fieldBoxElementRight {
    align-self: end;
    width: 50%;
  }

  .fieldBoxElementLeft {
    margin-right: 1rem;
  }

  .fieldBoxElementRight {
    margin-left: 1rem;
  }
}
