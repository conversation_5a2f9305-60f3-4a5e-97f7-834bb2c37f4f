@use 'src/styles/colors';

.textArea {
  width: 90%;
}

.flex {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin: 1rem auto;
}

.checkboxes {
  align-items: flex-start;
  align-self: flex-start;
  display: flex;
  flex-direction: column;
}

@mixin underline-color($color) {
  :global {
    .MuiInput-underline::before {
      border-bottom-color: $color !important;
    }

    .MuiInput-underline:hover:not(.Mui-disabled)::before {
      border-bottom-color: $color !important;
    }

    .MuiInput-underline::after {
      border-bottom-color: $color !important;
    }

    .MuiInput-underline:hover:not(.Mui-disabled)::after {
      border-bottom-color: $color !important;
    }
  }
}

@mixin border-color($color) {
  :global {
    flex-grow: 1;

    .MuiOutlinedInput-notchedOutline {
      border-color: $color !important;
    }

    .MuiOutlinedInput-root:hover .MuiOutlinedInput-notchedOutline {
      border-color: $color !important;
    }

    .MuiInputBase-root {
      margin-top: 20px;
    }
  }
}

.competeUnderline {
  @include underline-color(colors.$secondary-green);
}

.practiceUnderline {
  @include underline-color(colors.$success-background-color);
}

.competeBorder {
  @include border-color(colors.$secondary-green);
}

.practiceBorder {
  @include border-color(colors.$success-background-color);
}
