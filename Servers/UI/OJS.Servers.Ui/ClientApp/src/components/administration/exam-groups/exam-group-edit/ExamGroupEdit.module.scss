.form {
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  margin: 2rem auto;
  min-height: 400px;
  width: 100%;
}

.inputRow {
  width: 90%;
}

.textArea {
  width: 90%;
}

.flex {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin: 1rem auto;
}

.centralize {
  align-self: center;
  margin-bottom: 10px !important;
  margin-top: 20px !important;
  text-align: center;
}

.button {
  height: 3rem;
  margin: 0 10px;
  width: 10rem;
}

.buttonsWrapper {
  align-self: center;
  display: flex;
  justify-content: space-around;
  padding: 1rem;
  width: 30%;
}
