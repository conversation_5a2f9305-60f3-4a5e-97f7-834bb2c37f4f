@use 'src/styles/colors';
@use 'src/styles/fonts';
@use 'src/styles/variables';
@use 'src/styles/spacings';
@use 'src/styles/font-weights';

.profileAbout {
  display: flex;
  justify-content: space-between;
  margin-bottom: spacings.$sp-24;
  margin-top: spacings.$sp-24;
  padding: spacings.$sp-24;
}

.profileAboutInfo {
  align-self: flex-start;
  display: grid;
  font-size: variables.$f-size-20;
  gap: spacings.$sp-4 spacings.$sp-32;
  grid-template-columns: auto 1fr;
  margin-top: spacings.$sp-24;
}

.lightAboutInfo {
  background: colors.$light-base-color-100;
  border-bottom: 1px solid #e6e6e6;
}

.profileAboutInfoGroupControl {
  @extend %font-family-normal;

  display: contents;

  .profileAboutInfoLabel {
    color: colors.$primary-blue;
    font-weight: font-weights.$font-weight-medium;
    margin-bottom: spacings.$sp-8;
    text-align: left;
  }

  .profileAboutInfoValue {
    text-align: left;
  }
}

.imageAndLogoutButtonContainer {
  @extend %font-family-normal;

  align-items: center;
  display: flex;
  flex-direction: column;
}
