@use 'src/styles/colors.scss';
@use 'src/styles/spacings';

.backToTop {
  align-items: center;
  background-color: colors.$primary-blue;
  border: none;
  border-radius: 50%;
  bottom: spacings.$sp-32;
  cursor: pointer;
  display: flex;
  height: 45px;
  justify-content: center;
  position: fixed;
  transition: all 0.3s ease;
  width: 45px;
  z-index: 9999;

  &.lightTheme {
    box-shadow: 0 4px 12px rgb(68 169 248 / 20%);
    opacity: 0.9;

    &:hover {
      box-shadow: 0 6px 16px rgb(68 169 248 / 30%);
      opacity: 1;
    }

    &:focus {
      box-shadow: 0 0 0 3px rgb(68 169 248 / 30%);
    }
  }

  &.darkTheme {
    box-shadow: 0 4px 12px rgb(68 169 248 / 15%);
    opacity: 0.8;

    &:hover {
      box-shadow: 0 6px 16px rgb(68 169 248 / 25%);
      opacity: 1;
    }

    &:focus {
      box-shadow: 0 0 0 3px rgb(68 169 248 / 20%);
    }
  }

  &:hover {
    transform: translateY(-3px);
  }

  &:focus {
    outline: none;
  }

  &:active {
    transform: translateY(-1px);
  }

  svg {
    transition: transform 0.2s ease;
  }

  &:hover svg {
    transform: translateY(-2px);
  }
}
