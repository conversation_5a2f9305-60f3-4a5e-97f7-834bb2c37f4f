@use 'src/styles/colors';
@use 'src/styles/variables';
@use 'src/styles/components';
@use 'src/styles/fonts';
@use 'src/styles/spacings';
@use 'src/styles/font-weights';
@use 'src/styles/border-radiuses';

.searchContainer {
  align-items: center;
  border-radius: border-radiuses.$br-14;
  box-shadow: 0 2px 4px rgb(0 0 0 / 14%), 0 3px 4px rgb(0 0 0 / 12%), 0 1px 5px rgb(0 0 0 / 20%);
  display: flex;
  justify-content: space-between;
  left: 60px;
  margin-top: 78px;
  padding: 0 spacings.$sp-12;
  position: absolute;
  top: -100%;
  transition: top 0.3s ease-in-out;
  width: 90%;
  z-index: 2;
}

.show {
  top: 10px !important;
}

.search {
  align-items: center;
  display: flex;
  margin-right: 30px;
}

.closeIcon {
  cursor: pointer;
  transition: all 0.1s ease-in-out;

  &:hover {
    transform: scale(1.1);
  }
}

.searchInput {
  background-color: transparent;
  border: 1px solid colors.$primary-blue;
  font-size: variables.$f-size-16;
  height: 30px;
  margin: spacings.$sp-8;
  transition: transform 250ms ease-in-out, 250ms ease-in-out;
  width: 350px;

  &::placeholder {
    opacity: 1;
  }

  &:focus {
    outline: 0;
  }
}

.checkboxContainer {
  display: flex;
  margin-left: spacings.$sp-16;
}

.checkboxText {
  @extend %font-family-normal;

  color: colors.$text-dark-gray-color;
  font-size: variables.$f-size-16;
  font-weight: font-weights.$font-weight-light;
  line-height: 1.5;
  margin-left: spacings.$sp-4;
  margin-right: spacings.$sp-24;
}

.checkbox {
  height: 20px;
  width: 20px;
}

.checkboxWrapper {
  align-items: center;
  display: flex;
  justify-content: center;
}
